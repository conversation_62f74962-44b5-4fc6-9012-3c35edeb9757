import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import {
  ApiErrorResponse,
  RateLimitHeaders,
  ValidationResult,
  ValidationError,
  ErrorCode,
  ApiConfig
} from '../types/interfaces';
import { UserStorage } from '@/lib/userStorage';

// Extend AxiosRequestConfig to include metadata
interface EnhancedAxiosRequestConfig extends InternalAxiosRequestConfig {
  metadata?: {
    startTime: number;
  };
}

export class ApiClient {
  private client: AxiosInstance;
  private config: ApiConfig;
  private rateLimitHeaders: RateLimitHeaders | null = null;

  constructor(baseURL: string = '/api/v1', token?: string, config?: Partial<ApiConfig>) {
    this.config = {
      baseURL,
      timeout: 30000, // 30 seconds default
      enableLogging: false, // DISABLED for debug
      ...config
    };

    // Use provided token or get from user storage
    const authToken = token || UserStorage.getCurrentAccessToken();

    this.client = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {}
    });

    this.setupInterceptors();
  }

  /**
   * Update the authentication token
   * @param token New JWT Bearer token
   */
  setToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  removeToken(): void {
    delete this.client.defaults.headers.common['Authorization'];
  }

  /**
   * Get current rate limit information
   */
  getRateLimitHeaders(): RateLimitHeaders | null {
    return this.rateLimitHeaders;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...config };
    this.client.defaults.timeout = this.config.timeout;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config: EnhancedAxiosRequestConfig) => {
        // Add correlation ID for tracking
        if (!config.headers['X-Correlation-ID']) {
          config.headers['X-Correlation-ID'] = this.generateCorrelationId();
        }

        // Automatically refresh token from localStorage if not present
        if (!config.headers['Authorization']) {
          const currentToken = UserStorage.getCurrentAccessToken();
          if (currentToken && UserStorage.isUserContextValid()) {
            config.headers['Authorization'] = `Bearer ${currentToken}`;
          }
        }

        // Add user context headers
        const userContext = UserStorage.getUserContext();
        if (userContext) {
          config.headers['X-User-Id'] = userContext.userId;
          config.headers['X-User-Email'] = userContext.email;
          config.headers['X-User-Role'] = userContext.role;
        }

        // Add request timestamp for logging
        config.metadata = { startTime: Date.now() };

        if (this.config.enableLogging) {
          console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
            headers: config.headers,
            params: config.params,
            userId: userContext?.userId
          });
        }

        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse & { config: EnhancedAxiosRequestConfig }) => {
        // Extract rate limit headers
        this.extractRateLimitHeaders(response);

        // Log response
        if (this.config.enableLogging) {
          const duration = Date.now() - (response.config.metadata?.startTime || 0);
          console.log(`[API Response] ${response.status} ${response.config.url} (${duration}ms)`, {
            status: response.status,
            headers: response.headers
          });
        }

        return response;
      },
      async (error: AxiosError<ApiErrorResponse> & { config?: EnhancedAxiosRequestConfig }) => {
        const errorResponse = error.response?.data;
        const statusCode = error.response?.status;

        // Extract rate limit headers even on error
        if (error.response) {
          this.extractRateLimitHeaders(error.response);
        }

        if (this.config.enableLogging) {
          console.error('[API Error]', {
            url: error.config?.url,
            method: error.config?.method,
            status: statusCode,
            error: errorResponse,
            correlationId: error.config?.headers?.['X-Correlation-ID']
          });
        }

        // Rate limiting - no automatic retry, just reject the error

        // Handle specific error types
        const enhancedError = this.enhanceError(error, errorResponse);
        return Promise.reject(enhancedError);
      }
    );
  }

  private extractRateLimitHeaders(response: AxiosResponse): void {
    const headers = response.headers;

    if (headers['x-ratelimit-limit']) {
      this.rateLimitHeaders = {
        limit: parseInt(headers['x-ratelimit-limit']),
        remaining: parseInt(headers['x-ratelimit-remaining']),
        reset: parseInt(headers['x-ratelimit-reset'])
      };
    }
  }

  private enhanceError(error: AxiosError<ApiErrorResponse>, errorResponse?: ApiErrorResponse): ApiError {
    const statusCode = error.response?.status || 0;
    const message = errorResponse?.detail || error.message;
    const correlationId = (error.config as EnhancedAxiosRequestConfig)?.headers?.['X-Correlation-ID'] as string;

    return new ApiError(
      message,
      statusCode,
      errorResponse?.type || 'unknown',
      correlationId,
      errorResponse
    );
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }



  /**
   * Validate request parameters
   */
  private validateRequest(url: string, data?: any): ValidationResult {
    const errors: ValidationError[] = [];

    // Basic URL validation
    if (!url || typeof url !== 'string') {
      errors.push({
        field: 'url',
        message: 'URL is required and must be a string',
        code: 'INVALID_URL'
      });
    }

    // File upload validation
    if (url.includes('/upload') && data instanceof FormData) {
      const file = data.get('file') as File;
      if (file) {
        // File size validation (100MB limit for regular upload)
        const maxSize = url.includes('/chunked/') ? 5 * 1024 * 1024 * 1024 : 100 * 1024 * 1024; // 5GB for chunked, 100MB for regular
        if (file.size > maxSize) {
          errors.push({
            field: 'file',
            message: `File size exceeds limit of ${maxSize / 1024 / 1024}MB`,
            code: ErrorCode.FILE_TOO_LARGE
          });
        }

        // File type validation (basic check)
        if (file.size === 0) {
          errors.push({
            field: 'file',
            message: 'File cannot be empty',
            code: 'EMPTY_FILE'
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Enhanced HTTP methods with validation

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const validation = this.validateRequest(url);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.get<T>(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const validation = this.validateRequest(url, data);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const validation = this.validateRequest(url, data);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const validation = this.validateRequest(url);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.delete<T>(url, config);
    return response.data;
  }

  async downloadFile(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    const validation = this.validateRequest(url);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.get<Blob>(url, {
      ...config,
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Upload with progress tracking
   */
  async uploadWithProgress<T>(
    url: string,
    data: FormData,
    onProgress?: (progressEvent: any) => void,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const validation = this.validateRequest(url, data);
    if (!validation.isValid) {
      throw new ValidationApiError('Request validation failed', validation.errors);
    }

    const response = await this.client.post<T>(url, data, {
      ...config,
      onUploadProgress: onProgress,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    });

    return response.data;
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get<{ status: string }>('/health-check');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the underlying axios instance for advanced usage
   */
  getAxiosInstance(): AxiosInstance {
    return this.client;
  }
}

/**
 * Custom API Error class
 */
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly type: string;
  public readonly correlationId?: string;
  public readonly apiResponse?: ApiErrorResponse;

  constructor(
    message: string,
    statusCode: number,
    type: string,
    correlationId?: string,
    apiResponse?: ApiErrorResponse
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.type = type;
    this.correlationId = correlationId;
    this.apiResponse = apiResponse;
  }

  /**
   * Check if error is a specific type
   */
  isType(errorCode: ErrorCode): boolean {
    return this.apiResponse?.errors ?
      Object.values(this.apiResponse.errors).some(errors =>
        errors.some(error => error.includes(errorCode))
      ) : false;
  }

  /**
   * Get validation errors if available
   */
  getValidationErrors(): Record<string, string[]> | undefined {
    return this.apiResponse?.errors;
  }
}

/**
 * Validation-specific API Error
 */
export class ValidationApiError extends Error {
  public readonly validationErrors: ValidationError[];

  constructor(message: string, validationErrors: ValidationError[]) {
    super(message);
    this.name = 'ValidationApiError';
    this.validationErrors = validationErrors;
  }

  /**
   * Get errors for a specific field
   */
  getFieldErrors(field: string): ValidationError[] {
    return this.validationErrors.filter(error => error.field === field);
  }

  /**
   * Check if field has errors
   */
  hasFieldError(field: string): boolean {
    return this.validationErrors.some(error => error.field === field);
  }
}
