import { ApiClient } from '../core/apiClient';
import {
  FolderDto,
  FolderCreateData,
  FolderUpdateData,
  FolderContentsOptions,
  FolderContentsResponse,
  FolderDownloadOptions,
  ShareOptions,
  ShareDto,
  PermissionRequest,
  PermissionDto,
  PaginationInfo,
  SortField,
  SortDirection,
  FolderListApiResponse,
  FolderListOptions
} from '../types/interfaces';

export interface FolderListResponse {
  folders: FolderDto[];
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface FolderMoveOptions {
  targetParentFolderId?: string;
}

export interface BulkDeleteResponse {
  successful: string[];
  failed: { id: string; error: string }[];
}

export interface BulkDeleteOptions {
  folderIds: string[];
  force?: boolean;
  permanent?: boolean;
}

export class FolderService {
  constructor(private apiClient: ApiClient) {}

  /**
   * Create a new folder
   */
  async create(data: FolderCreateData): Promise<FolderDto> {
    return this.apiClient.post<FolderDto>('/folders', data);
  }


  /**
   * Get list of user folders with filtering and pagination
   */
  async getList(options: FolderListOptions = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();

    if (options.parentFolderId) params.append('parentFolderId', options.parentFolderId);
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.search) params.append('search', options.search);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);
    if (options.uploaderEmail) params.append('uploaderEmail', options.uploaderEmail);
    if (options.folderType && options.folderType !== 'all') {
      params.append('folderType', options.folderType);
    }
    if (options.createdAfter) params.append('createdAfter', options.createdAfter);
    if (options.createdBefore) params.append('createdBefore', options.createdBefore);
    if (options.includeShared !== undefined) {
      params.append('includeShared', options.includeShared.toString());
    }
    if (options.isArchived !== undefined) {
      params.append('isArchived', options.isArchived.toString());
    }

    const apiResponse = await this.apiClient.get<FolderListApiResponse>(`/folders?${params.toString()}`);
    
    // Handle different response formats for backward compatibility
    if ('data' in apiResponse && apiResponse.data) {
      // New wrapped API response format
      return {
        items: apiResponse.data.items || [],
        pagination: {
          page: apiResponse.data.page || 1,
          pageSize: apiResponse.data.pageSize || 20,
          totalItems: apiResponse.data.totalCount || 0,
          totalPages: apiResponse.data.totalPages || 0,
          hasNext: apiResponse.data.hasNextPage || false,
          hasPrevious: apiResponse.data.hasPreviousPage || false
        }
      };
    } else {
      // Fallback for direct response format (backward compatibility)
      const directResponse = apiResponse as any;
      return {
        items: directResponse.items || directResponse || [],
        pagination: directResponse.pagination || {
          page: 1,
          pageSize: 20,
          totalItems: Array.isArray(directResponse.items) ? directResponse.items.length : 0,
          totalPages: 1,
          hasNext: false,
          hasPrevious: false
        }
      };
    }
  }

  /**
   * Get folder details by ID
   */
  async getById(id: string): Promise<FolderDto> {
    return this.apiClient.get<FolderDto>(`/folders/${id}`);
  }

  /**
   * Update folder metadata
   */
  async update(id: string, data: FolderUpdateData): Promise<FolderDto> {
    return this.apiClient.put<FolderDto>(`/folders/${id}`, data);
  }

  /**
   * Delete folder (move to recycle bin by default)
   */
  async delete(id: string, permanent: boolean = false, force: boolean = false): Promise<void> {
    const params = new URLSearchParams();
    if (permanent) params.append('permanent', 'true');
    if (force) params.append('force', 'true');

    return this.apiClient.delete<void>(`/folders/${id}?${params.toString()}`);
  }

  /**
   * Get folder contents (files and subfolders)
   */
  async getContents(id: string, options: FolderContentsOptions = {}): Promise<FolderContentsResponse> {
    const params = new URLSearchParams();

    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    return this.apiClient.get<FolderContentsResponse>(`/folders/${id}/contents?${params.toString()}`);
  }

  /**
   * Move folder to a different parent
   */
  async move(id: string, options: FolderMoveOptions): Promise<FolderDto> {
    return this.apiClient.post<FolderDto>(`/folders/${id}/move`, options);
  }

  /**
   * Download folder as ZIP file
   */
  async download(id: string, options: FolderDownloadOptions = {}): Promise<Blob> {
    const params = new URLSearchParams();
    
    if (options.includeSubfolders !== undefined) {
      params.append('includeSubfolders', options.includeSubfolders.toString());
    }
    if (options.maxZipSize) {
      params.append('maxZipSize', options.maxZipSize.toString());
    }

    return this.apiClient.downloadFile(`/folders/${id}/download?${params.toString()}`);
  }

  /**
   * Bulk delete folders
   */
  async bulkDelete(ids: string[], permanent: boolean = false, force: boolean = false): Promise<BulkDeleteResponse> {
    const options: BulkDeleteOptions = {
      folderIds: ids,
      permanent,
      force
    };

    return this.apiClient.delete<BulkDeleteResponse>('/folders/bulk', { data: options });
  }

  /**
   * Create a shareable link for the folder
   */
  async createShare(id: string, options: ShareOptions & { includeSubfolders?: boolean }): Promise<ShareDto> {
    return this.apiClient.post<ShareDto>(`/folders/${id}/shares`, options);
  }

  /**
   * Get all shares for a folder
   */
  async getShares(id: string): Promise<ShareDto[]> {
    return this.apiClient.get<ShareDto[]>(`/folders/${id}/shares`);
  }

  /**
   * Delete a folder share
   */
  async deleteShare(folderId: string, shareId: string): Promise<void> {
    return this.apiClient.delete<void>(`/folders/${folderId}/shares/${shareId}`);
  }

  /**
   * Grant permission to a user or role for the folder
   */
  async grantPermission(id: string, request: PermissionRequest & { inheritToChildren?: boolean }): Promise<string> {
    const response = await this.apiClient.post<{ id: string }>(`/folders/${id}/permissions`, request);
    return response.id;
  }

  /**
   * Get all permissions for a folder
   */
  async getPermissions(id: string): Promise<PermissionDto[]> {
    return this.apiClient.get<PermissionDto[]>(`/folders/${id}/permissions`);
  }

  /**
   * Remove permission from a folder
   */
  async removePermission(folderId: string, permissionId: string): Promise<void> {
    return this.apiClient.delete<void>(`/folders/${folderId}/permissions/${permissionId}`);
  }

  /**
   * Get folder path (breadcrumb) - DISABLED
   * TODO: Implement breadcrumb functionality with new API structure
   */
  async getPath(id: string): Promise<FolderDto[]> {
    // Temporarily return empty array until new breadcrumb implementation
    return [];
  }

  /**
   * Copy folder to another location
   */
  async copy(id: string, targetParentFolderId?: string, newName?: string): Promise<FolderDto> {
    const options: { targetParentFolderId?: string; newName?: string } = {};
    if (targetParentFolderId !== undefined) options.targetParentFolderId = targetParentFolderId;
    if (newName) options.newName = newName;

    return this.apiClient.post<FolderDto>(`/folders/${id}/copy`, options);
  }

  /**
   * Get folder size and statistics
   */
  async getStatistics(id: string): Promise<{
    totalSize: number;
    fileCount: number;
    folderCount: number;
    lastModified: string;
    sizeByType: { mimeType: string; count: number; size: number }[];
  }> {
    return this.apiClient.get(`/folders/${id}/statistics`);
  }

  /**
   * Search folders
   */
  async search(query: string, options: Omit<FolderListOptions, 'search'> = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    return this.getList({ ...options, search: query });
  }

  /**
   * Get folder hierarchy (tree structure)
   */
  async getHierarchy(rootFolderId?: string, maxDepth?: number): Promise<FolderDto[]> {
    const params = new URLSearchParams();
    if (rootFolderId) params.append('rootFolderId', rootFolderId);
    if (maxDepth) params.append('maxDepth', maxDepth.toString());

    return this.apiClient.get<FolderDto[]>(`/folders/hierarchy?${params.toString()}`);
  }

  /**
   * Create folder structure from path
   */
  async createFromPath(path: string, parentFolderId?: string): Promise<FolderDto[]> {
    return this.apiClient.post<FolderDto[]>('/folders/create-path', {
      path,
      parentFolderId
    });
  }

  /**
   * Get recently accessed folders
   */
  async getRecentlyAccessed(limit: number = 10): Promise<FolderDto[]> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());

    return this.apiClient.get<FolderDto[]>(`/folders/recent?${params.toString()}`);
  }

  /**
   * Pin/unpin folder for quick access
   */
  async togglePin(id: string, pinned: boolean): Promise<FolderDto> {
    return this.apiClient.post<FolderDto>(`/folders/${id}/pin`, { pinned });
  }

  /**
   * Get pinned folders
   */
  async getPinned(): Promise<FolderDto[]> {
    return this.apiClient.get<FolderDto[]>('/folders/pinned');
  }

  /**
   * Duplicate folder with all contents
   */
  async duplicate(id: string, newName?: string, targetParentFolderId?: string): Promise<FolderDto> {
    const options: { newName?: string; targetParentFolderId?: string } = {};
    if (newName) options.newName = newName;
    if (targetParentFolderId !== undefined) options.targetParentFolderId = targetParentFolderId;

    return this.apiClient.post<FolderDto>(`/folders/${id}/duplicate`, options);
  }

  /**
   * Archive folder (compress and store)
   */
  async compress(id: string): Promise<{ archiveId: string; downloadUrl: string }> {
    return this.apiClient.post(`/folders/${id}/archive`);
  }

  /**
   * Get folder activity log
   */
  async getActivityLog(id: string, options: { page?: number; pageSize?: number } = {}): Promise<{
    activities: Array<{
      id: string;
      action: string;
      userId: string;
      userName: string;
      timestamp: string;
      details: Record<string, any>;
    }>;
    pagination: PaginationInfo;
  }> {
    const params = new URLSearchParams();
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());

    return this.apiClient.get(`/folders/${id}/activity?${params.toString()}`);
  }

  /**
   * Set folder as template
   */
  async setAsTemplate(id: string, templateName: string, description?: string): Promise<void> {
    return this.apiClient.post<void>(`/folders/${id}/template`, {
      templateName,
      description
    });
  }

  /**
   * Create folder from template
   */
  async createFromTemplate(templateId: string, name: string, parentFolderId?: string): Promise<FolderDto> {
    return this.apiClient.post<FolderDto>('/folders/from-template', {
      templateId,
      name,
      parentFolderId
    });
  }

  /**
   * Get available folder templates
   */
  async getTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    createdAt: string;
    createdBy: string;
  }>> {
    return this.apiClient.get('/folders/templates');
  }

  /**
   * Archive a folder and all its contents (only owner can perform this action)
   */
  async archive(id: string): Promise<boolean> {
    const response = await this.apiClient.post<{ data: boolean }>(`/folders/${id}/archive`);
    return response.data;
  }

  /**
   * Unarchive a folder and all its contents (restore from archived state)
   */
  async unarchive(id: string): Promise<boolean> {
    const response = await this.apiClient.post<{ data: boolean }>(`/folders/${id}/unarchive`);
    return response.data;
  }
} 