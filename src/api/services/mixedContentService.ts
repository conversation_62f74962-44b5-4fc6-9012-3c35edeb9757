import { ApiClient } from '../core/apiClient';
import { FolderService } from './folderService';
import { FileService } from './fileService';
import {
  MixedContentItem,
  MixedContentResponse,
  MixedContentOptions,
  MixedContentSortBy,
  FolderDto,
  FileDto,
  SortDirection,
  PaginationInfo
} from '../types/interfaces';

export class MixedContentService {
  private folderService: FolderService;
  private fileService: FileService;

  constructor(apiClient: ApiClient) {
    this.folderService = new FolderService(apiClient);
    this.fileService = new FileService(apiClient);
  }

  /**
   * Get mixed content (folders + files) for a specific parent folder
   */
  async getList(options: MixedContentOptions = {}): Promise<MixedContentResponse> {
    try {
      // Prepare API calls
      const promises: Promise<any>[] = [];
      
      // Only call folder API if we want folders
      if (options.itemType === 'all' || options.itemType === 'folders' || !options.itemType) {
        promises.push(this.folderService.getList({
          parentFolderId: options.parentFolderId,
          page: 1, // Get all folders first, we'll handle pagination later
          pageSize: 1000, // Large number to get all folders
          search: options.search,
          sortBy: this.mapSortField(options.sortBy),
          sortDirection: options.sortDirection,
          includeShared: options.includeShared,
          isArchived: options.isArchived
        }));
      } else {
        promises.push(Promise.resolve({ items: [], pagination: this.getEmptyPagination() }));
      }

      // Only call file API if we want files
      if (options.itemType === 'all' || options.itemType === 'files' || !options.itemType) {
        promises.push(this.fileService.getList({
          parentFolderId: options.parentFolderId,
          page: 1, // Get all files first, we'll handle pagination later
          pageSize: 1000, // Large number to get all files
          search: options.search,
          sortBy: this.mapSortField(options.sortBy),
          sortDirection: options.sortDirection,
          uploaderEmail: options.uploaderEmail,
          mimeType: options.mimeType,
          createdAfter: options.createdAfter,
          createdBefore: options.createdBefore,
          includeShared: options.includeShared,
          isArchived: options.isArchived
        }));
      } else {
        promises.push(Promise.resolve({ items: [], pagination: this.getEmptyPagination() }));
      }

      // Execute both API calls in parallel
      const [folderResponse, fileResponse] = await Promise.all(promises);

      // Convert to mixed content items
      const folderItems = this.convertFoldersToMixedItems(folderResponse.items);
      const fileItems = this.convertFilesToMixedItems(fileResponse.items);

      // Combine and sort
      const allItems = [...folderItems, ...fileItems];
      const sortedItems = this.sortMixedItems(allItems, options.sortBy, options.sortDirection);

      // Apply pagination to the combined result
      const paginatedResult = this.applyPagination(
        sortedItems,
        options.page || 1,
        options.pageSize || 20
      );

      return {
        items: paginatedResult.items,
        pagination: paginatedResult.pagination,
        folderCount: folderItems.length,
        fileCount: fileItems.length
      };

    } catch (error) {
      console.error('Error fetching mixed content:', error);
      throw error;
    }
  }

  /**
   * Convert folder DTOs to mixed content items
   */
  private convertFoldersToMixedItems(folders: FolderDto[]): MixedContentItem[] {
    return folders.map(folder => ({
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      parentFolderId: folder.parentFolderId,
      ownerId: folder.ownerId,
      ownerName: folder.ownerName,
      createdBy: folder.createdBy,
      createdByName: folder.createdByName,
      updatedBy: folder.updatedBy,
      updatedByName: folder.updatedByName,
      createdAt: folder.createdAt,
      updatedAt: folder.updatedAt,
      isArchived: folder.isArchived,
      archivedAt: folder.archivedAt,
      archivedBy: folder.archivedBy,
      archivedByName: folder.archivedByName,
      permissions: folder.permissions,
      // Folder specific
      path: folder.path,
      level: folder.level,
      fileCount: folder.fileCount,
      subfolderCount: folder.subfolderCount,
      description: folder.description
    }));
  }

  /**
   * Convert file DTOs to mixed content items
   */
  private convertFilesToMixedItems(files: FileDto[]): MixedContentItem[] {
    return files.map(file => ({
      id: file.id,
      name: file.displayName || file.name,
      type: 'file' as const,
      parentFolderId: file.parentFolderId,
      ownerId: file.ownerId,
      ownerName: file.ownerName || '', // Use the new ownerName field
      createdBy: file.createdBy || file.ownerId, // Use createdBy if available, fallback to ownerId
      createdByName: file.createdByName || '',
      updatedBy: file.updatedBy || file.ownerId,
      updatedByName: file.updatedByName || '',
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
      isArchived: file.isArchived,
      archivedAt: file.archivedAt,
      archivedBy: file.archivedBy,
      archivedByName: file.archivedByName,
      permissions: file.permissions,
      // File specific
      size: file.fileSize,
      mimeType: file.mimeType,
      extension: this.getFileExtension(file.name),
      displayName: file.displayName,
      tags: file.tags,
      version: file.version,
      checksumMd5: file.hashMd5,
      customMetadata: file.customMetadata,
      description: file.description
    }));
  }

  /**
   * Sort mixed content items
   */
  private sortMixedItems(
    items: MixedContentItem[],
    sortBy?: MixedContentSortBy,
    sortDirection: SortDirection = SortDirection.ASC
  ): MixedContentItem[] {
    const isAscending = sortDirection === SortDirection.ASC;

    return items.sort((a, b) => {
      // First priority: Always show folders before files (Google Drive style)
      if (sortBy !== 'type') {
        if (a.type !== b.type) {
          return a.type === 'folder' ? -1 : 1;
        }
      }

      // Second priority: Sort by specified field
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name, 'vi', { sensitivity: 'base' });
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'updatedAt':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
          break;
        case 'size':
          // Folders have no size, files have size
          const aSize = a.type === 'folder' ? 0 : (a.size || 0);
          const bSize = b.type === 'folder' ? 0 : (b.size || 0);
          comparison = aSize - bSize;
          break;
        case 'type':
          // When sorting by type, allow files before folders if DESC
          comparison = a.type.localeCompare(b.type);
          break;
        default:
          // Default sort by name
          comparison = a.name.localeCompare(b.name, 'vi', { sensitivity: 'base' });
      }

      return isAscending ? comparison : -comparison;
    });
  }

  /**
   * Apply pagination to sorted items
   */
  private applyPagination(
    items: MixedContentItem[],
    page: number,
    pageSize: number
  ): { items: MixedContentItem[]; pagination: PaginationInfo } {
    const totalItems = items.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1
      }
    };
  }

  /**
   * Map mixed content sort fields to API sort fields
   */
  private mapSortField(sortBy?: MixedContentSortBy): any {
    switch (sortBy) {
      case 'name': return 'Name';
      case 'createdAt': return 'CreatedAt';
      case 'updatedAt': return 'UpdatedAt';
      case 'size': return 'FileSize';
      default: return 'Name';
    }
  }

  /**
   * Extract file extension from filename
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';
  }

  /**
   * Get empty pagination info
   */
  private getEmptyPagination(): PaginationInfo {
    return {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0,
      hasNext: false,
      hasPrevious: false
    };
  }

  /**
   * Get file type icon based on extension or mime type
   */
  static getFileTypeInfo(item: MixedContentItem): {
    icon: string;
    color: string;
    category: string;
  } {
    if (item.type === 'folder') {
      return {
        icon: '📁',
        color: 'text-blue-600',
        category: 'Folder'
      };
    }

    const extension = item.extension?.toLowerCase() || '';
    const mimeType = item.mimeType?.toLowerCase() || '';

    // Document types
    if (['pdf'].includes(extension) || mimeType.includes('pdf')) {
      return { icon: '📄', color: 'text-red-600', category: 'PDF' };
    }
    if (['doc', 'docx'].includes(extension) || mimeType.includes('word')) {
      return { icon: '📝', color: 'text-blue-600', category: 'Word' };
    }
    if (['xls', 'xlsx'].includes(extension) || mimeType.includes('excel')) {
      return { icon: '📊', color: 'text-green-600', category: 'Excel' };
    }
    if (['ppt', 'pptx'].includes(extension) || mimeType.includes('powerpoint')) {
      return { icon: '📋', color: 'text-orange-600', category: 'PowerPoint' };
    }

    // Image types
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension) || mimeType.startsWith('image/')) {
      return { icon: '🖼️', color: 'text-purple-600', category: 'Image' };
    }

    // Video types
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension) || mimeType.startsWith('video/')) {
      return { icon: '🎥', color: 'text-red-600', category: 'Video' };
    }

    // Audio types
    if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension) || mimeType.startsWith('audio/')) {
      return { icon: '🎵', color: 'text-pink-600', category: 'Audio' };
    }

    // Archive types
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return { icon: '📦', color: 'text-gray-600', category: 'Archive' };
    }

    // Text types
    if (['txt', 'md', 'json', 'xml', 'csv'].includes(extension) || mimeType.startsWith('text/')) {
      return { icon: '📃', color: 'text-gray-600', category: 'Text' };
    }

    // Default
    return { icon: '📄', color: 'text-gray-500', category: 'File' };
  }
} 