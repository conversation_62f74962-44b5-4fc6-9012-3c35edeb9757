"use client";

import React, { useState, useEffect } from "react";
import { UploadZone } from "@/components/ui/UploadZone";
import { FileList } from "@/components/ui/FileList";
import { ApiClient } from "@/api/core/apiClient";
import { FileService } from "@/api/services/fileService";
import { FolderService } from "@/api/services/folderService";
import { FileDto, FolderDto } from "@/api/types/interfaces";
import { FileSystemItem, FileItem, FolderItem } from "@/types";
import { useAuth } from "@/contexts/AuthContext";
import { UserStorage } from "@/lib/userStorage";
import {
  DocumentIcon,
  EyeIcon,
  ClockIcon,
  CogIcon,
} from "@heroicons/react/24/outline";

export default function Dashboard() {
  const { isAuthenticated, login, user, getCurrentUserId } = useAuth();
  const [files, setFiles] = useState<FileSystemItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiClient, setApiClient] = useState<ApiClient | null>(null);
  const [fileService, setFileService] = useState<FileService | null>(null);
  const [folderService, setFolderService] = useState<FolderService | null>(null);

  // Initialize API client when authenticated
  useEffect(() => {
    if (isAuthenticated && (user?.access_token || UserStorage.getCurrentAccessToken())) {
      // The ApiClient will automatically use the token from UserStorage if not provided
      const client = new ApiClient(process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040');
      setApiClient(client);
      setFileService(new FileService(client));
      setFolderService(new FolderService(client));

      // Log current user context for debugging
      const userContext = UserStorage.getUserContext();
      const userId = getCurrentUserId();
      console.log("🔧 API Client initialized with user context:", {
        userId,
        email: userContext?.email,
        role: userContext?.role,
        hasValidToken: UserStorage.isUserContextValid()
      });
    }
  }, [isAuthenticated, user?.access_token, getCurrentUserId]);

  useEffect(() => {
    const loadFiles = async () => {
      if (!fileService || !folderService || !isAuthenticated) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        // Check if we're in a level 1 folder context - if so, don't call APIs
        // This logic prevents unnecessary API calls for top-level navigation
        const isLevel1Context = window.location.pathname === '/' || 
                                window.location.pathname === '/page' ||
                                !window.location.search; // No query params = root level
        
        if (isLevel1Context) {
          console.log("🚫 Level 1 folder detected - skipping API calls for files");
          setFiles([]);
          setError(null);
          setIsLoading(false);
          return;
        }

        // Get folders and files with proper error handling
        const [foldersResponse, filesResponse] = await Promise.all([
          folderService.getList({ pageSize: 50 }).catch(err => {
            console.error("Error fetching folders:", err);
            return { items: [], pagination: { page: 1, pageSize: 50, totalItems: 0, totalPages: 0, hasNext: false, hasPrevious: false } };
          }),
          fileService.getList({ pageSize: 50 }).catch(err => {
            console.error("Error fetching files:", err);
            return { items: [], pagination: { page: 1, pageSize: 50, totalItems: 0, totalPages: 0, hasNext: false, hasPrevious: false } };
          })
        ]);

        // Safely combine files and folders with null checks
        const combinedFiles: FileSystemItem[] = [
          // Folders - with null safety
          ...(foldersResponse?.items || []).map((folder: FolderDto): FolderItem => ({
            id: folder.id,
            name: folder.name,
            type: 'folder' as const,
            createdAt: new Date(folder.createdAt),
            updatedAt: new Date(folder.updatedAt),
          })),
          // Files - with null safety
          ...(filesResponse?.items || []).map((file: FileDto): FileItem => ({
            id: file.id,
            name: file.displayName || file.name,
            type: 'file' as const,
            size: file.fileSize,
            createdAt: new Date(file.createdAt),
            updatedAt: new Date(file.updatedAt),
          })),
        ];
        
        setFiles(combinedFiles);
        setError(null);
      } catch (err) {
        console.error("Error loading files:", err);
        setError("Failed to load files. Please try again.");
        setFiles([]); // Set empty array on error
      } finally {
        setIsLoading(false);
      }
    };

    loadFiles();
  }, [fileService, folderService, isAuthenticated]);

  const stats = [
    {
      name: "Total Files",
      value: files.filter((f) => f.type === "file").length,
      icon: DocumentIcon,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      name: "Folders",
      value: files.filter((f) => f.type === "folder").length,
      icon: CogIcon,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      name: "Recent Views",
      value: 0,
      icon: EyeIcon,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      name: "Active Jobs",
      value: 0,
      icon: ClockIcon,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ];

  const handleFilesSelected = async (files: File[]) => {
    if (!fileService) return;

    try {
      // Upload each file
      for (const file of files) {
        await fileService.upload(file);
      }

      // Refresh the file list after upload with proper error handling
      const [foldersResponse, filesResponse] = await Promise.all([
        folderService!.getList({ pageSize: 50 }).catch(err => {
          console.error("Error fetching folders after upload:", err);
          return { items: [], pagination: { page: 1, pageSize: 50, totalItems: 0, totalPages: 0, hasNext: false, hasPrevious: false } };
        }),
        fileService.getList({ pageSize: 50 }).catch(err => {
          console.error("Error fetching files after upload:", err);
          return { items: [], pagination: { page: 1, pageSize: 50, totalItems: 0, totalPages: 0, hasNext: false, hasPrevious: false } };
        })
      ]);

      // Safely combine files and folders with null checks
      const combinedFiles: FileSystemItem[] = [
        // Folders - with null safety
        ...(foldersResponse?.items || []).map((folder: FolderDto): FolderItem => ({
          id: folder.id,
          name: folder.name,
          type: 'folder' as const,
          createdAt: new Date(folder.createdAt),
          updatedAt: new Date(folder.updatedAt),
        })),
        // Files - with null safety
        ...(filesResponse?.items || []).map((file: FileDto): FileItem => ({
          id: file.id,
          name: file.displayName || file.name,
          type: 'file' as const,
          size: file.fileSize,
          createdAt: new Date(file.createdAt),
          updatedAt: new Date(file.updatedAt),
        })),
      ];
      setFiles(combinedFiles);
    } catch (error) {
      console.error("Error uploading files:", error);
      setError("Upload failed. Please try again.");
    }
  };

  const handleFileSelect = (file: FileSystemItem) => {
    if (file.type === "file") {
      // Navigate to viewer for files
      window.location.href = `/viewer?fileId=${file.id}`;
    } else {
      // Navigate to folder manager
      window.location.href = `/manager-folders?folderId=${file.id}`;
    }
  };

  const handleFileDelete = async (fileId: string) => {
    if (!fileService || !folderService) return;

    try {
      // Determine if it's a file or folder
      const item = files.find((f) => f.id === fileId);

      if (item?.type === "file") {
        await fileService.delete(fileId, false); // Move to recycle bin
      } else if (item?.type === "folder") {
        await folderService.delete(fileId, false, false); // Move to recycle bin, not recursive
      }

      // Update state by removing the deleted item
      setFiles(files.filter((f) => f.id !== fileId));
    } catch (error) {
      console.error("Error deleting item:", error);
      setError("Deletion failed. Please try again.");
    }
  };

  const handleFolderCreate = async (name: string) => {
    if (!folderService) return;

    try {
      const newFolder = await folderService.create({ name });
      const newFolderItem: FolderItem = {
        id: newFolder.id,
        name: newFolder.name,
        type: 'folder' as const,
        createdAt: new Date(newFolder.createdAt),
        updatedAt: new Date(newFolder.updatedAt)
      };
      setFiles([...files, newFolderItem]);
    } catch (error) {
      console.error("Error creating folder:", error);
      setError("Failed to create folder. Please try again.");
    }
  };

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Welcome to PDF Management System
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Please sign in to continue
            </p>
          </div>
          <div>
            <button
              onClick={login}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Sign in with SSO
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
          <button
            className="absolute top-0 right-0 p-2"
            onClick={() => setError(null)}
          >
            <span className="sr-only">Close</span>
            <svg
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            Manage your PDF files and OCR operations
          </p>
        </div>
        
        {/* User Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Current User</h3>
          {(() => {
            const userDisplay = UserStorage.getUserDisplayInfo();
            const userId = getCurrentUserId();
            return userDisplay ? (
              <div className="text-sm text-blue-700 space-y-1">
                <div><strong>Name:</strong> {userDisplay.name}</div>
                <div><strong>Email:</strong> {userDisplay.email}</div>
                <div><strong>Role:</strong> {userDisplay.role}</div>
                <div><strong>User ID:</strong> <code className="bg-blue-100 px-1 rounded text-xs">{userId}</code></div>
              </div>
            ) : (
              <p className="text-sm text-blue-600">Loading user info...</p>
            );
          })()}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div
            key={stat.name}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-md ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Zone */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Upload Files
          </h2>
          <UploadZone
            onFilesSelected={handleFilesSelected}
            accept="application/pdf"
            multiple={true}
          />
        </div>

        {/* Files List */}
        <div>
          <FileList
            files={files}
            onFileSelect={handleFileSelect}
            onFileDelete={handleFileDelete}
            onFolderCreate={handleFolderCreate}
          />
        </div>
      </div>
    </div>
  );
}
