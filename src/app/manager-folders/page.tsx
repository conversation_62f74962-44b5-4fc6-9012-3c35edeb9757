"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  FolderIcon, 
  DocumentIcon, 
  PlusIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  UserIcon,
  CalendarDaysIcon,
  EllipsisVerticalIcon,
  FolderPlusIcon,
  CloudArrowUpIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import { CreateFolderModal } from '@/components/modals/CreateFolderModal';
import { FolderService } from '@/api/services/folderService';
import { FileService } from '@/api/services/fileService';
import { MixedContentService } from '@/api/services/mixedContentService';
import { GoogleDriveService } from '@/api/services/googleDriveService';
import { ApiClient } from '@/api/core/apiClient';
import { useAuth } from '@/contexts/AuthContext';
import {
  FolderDto,
  FileDto,
  SortField,
  SortDirection,
  FolderContentsResponse,
  FolderCreateData,
  MixedContentItem,
  MixedContentOptions,
  MixedContentSortBy,
  StorageProvider
} from '@/api/types/interfaces';

interface FolderManagerState {
  currentFolder: FolderDto | null;
  breadcrumb: FolderDto[];
  mixedItems: MixedContentItem[]; // Combined folders and files
  folders: FolderDto[]; // Separate folders array for easier access
  files: FileDto[]; // Separate files array for easier access
  selectedItems: Set<string>;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  filters: {
    itemType: 'all' | 'folders' | 'files'; // Filter by type like Google Drive
    folderType: 'all' | 'public' | 'private' | 'shared';
    uploaderEmail: string;
    mimeType: string; // Filter by file type
    dateRange: {
      from: string;
      to: string;
    };
  };
  sorting: {
    field: MixedContentSortBy; // Use mixed content sort options
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    folderCount: number;
    fileCount: number;
  };
  viewMode: 'list' | 'grid';
  showNewFolderModal: boolean;
  showBulkActions: boolean;
}

export default function FolderManagerPage() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Fix API client initialization - use access_token directly from user
  const apiClient = new ApiClient(
    process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040',
    user?.access_token || ''
  );
  
  const [state, setState] = useState<FolderManagerState>({
    currentFolder: null,
    breadcrumb: [],
    mixedItems: [],
    folders: [],
    files: [],
    selectedItems: new Set(),
    loading: false,
    error: null,
    searchTerm: "",
    filters: {
      itemType: 'all',
      folderType: 'all',
      uploaderEmail: '',
      mimeType: '',
      dateRange: { from: '', to: '' }
    },
    sorting: {
      field: 'createdAt',
      direction: SortDirection.DESC
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0,
      folderCount: 0,
      fileCount: 0
    },
    viewMode: 'list',
    showNewFolderModal: false,
    showBulkActions: false
  });

  const folderService = new FolderService(apiClient);
  const fileService = new FileService(apiClient);
  const mixedContentService = new MixedContentService(apiClient);
  const googleDriveService = new GoogleDriveService(apiClient);

  // Load root folders only (no files, no API call for file listing) - RESTORED
  const loadRootFolders = useCallback(async () => {
    if (!user?.access_token) return;
    
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await folderService.getRootFolders({
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        sortBy: state.sorting.field as SortField,
        sortDirection: state.sorting.direction
      });

      setState(prev => ({
        ...prev,
        currentFolder: null,
        breadcrumb: [],
        folders: response.data.items || [],
        files: [], // Root level has no files
        mixedItems: (response.data.items || []).map(folder => ({ ...folder, itemType: 'folder' as const })),
        pagination: {
          ...prev.pagination,
          totalItems: response.data.totalCount || 0,
          totalPages: response.data.totalPages || 0,
          folderCount: response.data.totalCount || 0,
          fileCount: 0
        },
        loading: false,
        error: null
      }));
    } catch (error) {
      // Simple error handling - just log and stop loading
      console.log('Failed to load root folders:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        folders: [],
        files: [],
        mixedItems: []
      }));
    }
  }, [user?.access_token, state.pagination.page, state.pagination.pageSize, state.sorting.field, state.sorting.direction, folderService]);

  // Load folder contents using mixed content service - RESTORED
  const loadFolderContents = useCallback(async (folderId?: string) => {
    if (!user?.access_token) return;
    
    setState(prev => ({ ...prev, loading: true }));

    try {
      if (!folderId) {
        await loadRootFolders();
        return;
      }

      // Get folder details first
      const folderDetails = await folderService.getFolderById(folderId);
      
      // Get mixed content
      const options: MixedContentOptions = {
        folderId,
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        sortBy: state.sorting.field,
        sortDirection: state.sorting.direction,
        searchTerm: state.searchTerm || undefined,
        itemType: state.filters.itemType !== 'all' ? state.filters.itemType : undefined
      };

      const mixedResponse = await mixedContentService.getMixedContent(options);
      
      // Build breadcrumb
      const breadcrumb: FolderDto[] = [];
      if (folderDetails.data.parentFolderId) {
        // For simplicity, just add the current folder to breadcrumb
        // In a real app, you'd build the full path
        breadcrumb.push(folderDetails.data);
      }

      setState(prev => ({
        ...prev,
        currentFolder: folderDetails.data,
        breadcrumb,
        mixedItems: mixedResponse.data.items || [],
        folders: (mixedResponse.data.items || [])
          .filter(item => item.itemType === 'folder')
          .map(item => item as FolderDto),
        files: (mixedResponse.data.items || [])
          .filter(item => item.itemType === 'file')
          .map(item => item as FileDto),
        pagination: {
          ...prev.pagination,
          totalItems: mixedResponse.data.totalCount || 0,
          totalPages: mixedResponse.data.totalPages || 0,
          folderCount: mixedResponse.data.folderCount || 0,
          fileCount: mixedResponse.data.fileCount || 0
        },
        loading: false,
        error: null
      }));
    } catch (error) {
      // Simple error handling - just log and stop loading
      console.log('Failed to load folder contents:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        currentFolder: null,
        breadcrumb: [],
        folders: [],
        files: [],
        mixedItems: []
      }));
    }
  }, [user?.access_token, state.pagination.page, state.pagination.pageSize, state.sorting, state.searchTerm, state.filters.itemType, folderService, mixedContentService, loadRootFolders]);

  // Read folder ID from URL and initialize component - RESTORED
  useEffect(() => {
    if (user?.access_token) {
      const folderIdFromUrl = searchParams.get('folderId');
      if (folderIdFromUrl) {
        loadFolderContents(folderIdFromUrl);
      } else {
        // At root level - only load root folders, no file listing API calls
        loadRootFolders();
      }
    }
  }, [user?.access_token, searchParams, loadRootFolders, loadFolderContents]);

  // Reload when dependencies change - RESTORED
  useEffect(() => {
    if (user?.access_token && state.currentFolder?.id) {
      loadFolderContents(state.currentFolder.id);
    }
  }, [state.pagination.page, state.pagination.pageSize, state.sorting, loadFolderContents, user?.access_token, state.currentFolder?.id]);

  // Handle folder navigation - RESTORED
  const navigateToFolder = (folderId: string) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    
    // Update URL with folder ID
    const params = new URLSearchParams(searchParams.toString());
    params.set('folderId', folderId);
    router.push(`/manager-folders?${params.toString()}`);
    
    // Load folder content
    loadFolderContents(folderId);
  };

  // Handle breadcrumb navigation - RESTORED
  const navigateToBreadcrumb = (index: number) => {
    if (index === 0) {
      // Navigate to root - remove folderId from URL
      const params = new URLSearchParams(searchParams.toString());
      params.delete('folderId');
      const queryString = params.toString();
      router.push(`/manager-folders${queryString ? `?${queryString}` : ''}`);
      
      // Load root content
      loadRootFolders();
    } else {
      navigateToFolder(state.breadcrumb[index].id);
    }
  };

  // Handle search - RESTORED
  const handleSearch = useCallback(async () => {
    if (!user?.access_token) return;
    
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    
    // Reload current content with search
    if (state.currentFolder?.id) {
      loadFolderContents(state.currentFolder.id);
    } else {
      loadRootFolders();
    }
  }, [user?.access_token, state.currentFolder?.id, loadFolderContents, loadRootFolders]);

  // Handle sorting - RESTORED
  const handleSort = (field: MixedContentSortBy) => {
    setState(prev => ({
      ...prev,
      sorting: {
        field,
        direction: prev.sorting.field === field && prev.sorting.direction === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC
      },
      pagination: { ...prev.pagination, page: 1 }
    }));
    
    // Reload with new sorting
    if (state.currentFolder?.id) {
      loadFolderContents(state.currentFolder.id);
    } else {
      loadRootFolders();
    }
  };

  // Handle pagination - RESTORED
  const handlePageChange = (page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));
    
    // Reload with new page
    if (state.currentFolder?.id) {
      loadFolderContents(state.currentFolder.id);
    } else {
      loadRootFolders();
    }
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState(prev => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    setState(prev => {
      const newSelection = new Set<string>();
      if (checked) {
        [...prev.folders, ...prev.files].forEach(item => newSelection.add(item.id));
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Create new folder - RESTORED
  const handleCreateFolder = async (name: string, description?: string) => {
    if (!user?.access_token) return;
    
    try {
      const createData: FolderCreateData = {
        name,
        description,
        parentFolderId: state.currentFolder?.id || null,
        isPublic: false
      };
      
      await folderService.createFolder(createData);
      
      setState(prev => ({ ...prev, showNewFolderModal: false }));
      
      // Reload current content
      if (state.currentFolder?.id) {
        loadFolderContents(state.currentFolder.id);
      } else {
        loadRootFolders();
      }
    } catch (error) {
      console.log('Failed to create folder:', error);
      setState(prev => ({ ...prev, showNewFolderModal: false }));
    }
  };

  // Bulk delete - RESTORED
  const handleBulkDelete = async () => {
    if (!user?.access_token || state.selectedItems.size === 0) return;
    
    try {
      const selectedIds = Array.from(state.selectedItems);
      
      // Simple approach - just log for now
      console.log('Bulk delete items:', selectedIds);
      
      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      
      // Reload current content
      if (state.currentFolder?.id) {
        loadFolderContents(state.currentFolder.id);
      } else {
        loadRootFolders();
      }
    } catch (error) {
      console.log('Failed to delete items:', error);
      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  // Navigate back to root - RESTORED
  const navigateToRoot = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete('folderId');
    const queryString = params.toString();
    router.push(`/manager-folders${queryString ? `?${queryString}` : ''}`);
    
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    
    // Load root content
    loadRootFolders();
  };

  // Debug function to test API response mapping
  const testApiResponseMapping = () => {
    const sampleApiResponse = {
      "data": {
        "items": [
          {
            "id": "bc3ad824-7bda-445c-8051-5b7862c5ac09",
            "name": "Bùi Doanh Thái",
            "parentFolderId": null,
            "ownerId": "ca91af00-3762-48b8-a047-5e10ffca4f0d",
            "ownerName": "User-ca91af00",
            "createdBy": "00000000-0000-0000-0000-000000000000",
            "createdByName": "Unknown User",
            "updatedBy": null,
            "updatedByName": null,
            "path": "/Bùi Doanh Thái",
            "level": 0,
            "createdAt": "2025-06-28T06:46:52.614462Z",
            "updatedAt": "2025-06-28T06:46:52.614462Z",
            "fileCount": 0,
            "subfolderCount": 0,
            "permissions": []
          }
        ],
        "totalCount": 3,
        "page": 1,
        "pageSize": 20,
        "totalPages": 1,
        "hasNextPage": false,
        "hasPreviousPage": false
      },
      "message": "Folders retrieved successfully",
      "statusCode": 200,
      "success": true,
      "errorCode": null
    };

    console.log('Sample API Response:', sampleApiResponse);
    console.log('Mapped Folders:', sampleApiResponse.data.items);
    console.log('First folder owner:', sampleApiResponse.data.items[0]?.ownerName);
  };

  // Loading state
  if (!user?.access_token) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-body text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {state.currentFolder && (
                <button
                  onClick={navigateToRoot}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Về thư mục gốc"
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>
              )}
              <div>
                <h1 className="text-heading-2 text-gray-900">
                  {state.currentFolder ? state.currentFolder.name : 'Quản lý Folder'}
                </h1>
                <p className="text-body text-gray-600 mt-1">
                  {state.currentFolder 
                    ? `Đang xem folder: ${state.currentFolder.path}` 
                    : 'Quản lý và tổ chức folders của bạn'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                className="btn-primary flex items-center gap-2"
              >
                <FolderPlusIcon className="w-4 h-4" />
                Tạo Folder Mới
              </button>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{state.error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setState(prev => ({ ...prev, error: null }))}
                  className="text-red-400 hover:text-red-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Breadcrumb */}
        {state.breadcrumb.length > 0 && (
          <nav className="flex mb-6 animate-fade-in" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-1">
              <li>
                <button
                  onClick={() => navigateToBreadcrumb(0)}
                  className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
                >
                  Root
                </button>
              </li>
              {state.breadcrumb.map((folder, index) => (
                <li key={folder.id} className="flex items-center">
                  <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-1" />
                  <button
                    onClick={() => navigateToBreadcrumb(index)}
                    className={`font-medium transition-colors ${
                      index === state.breadcrumb.length - 1
                        ? 'text-gray-900'
                        : 'text-blue-600 hover:text-blue-800'
                    }`}
                  >
                    {folder.name}
                  </button>
                </li>
              ))}
            </ol>
          </nav>
        )}

        {/* Filters and Search */}
        <div className="card p-6 mb-6 animate-fade-in">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo Tên Folder/ File"
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="input pl-10 w-full"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-3">
              <select
                value={state.filters.folderType}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, folderType: e.target.value as any }
                }))}
                className="input"
              >
                <option value="all">Tất cả loại</option>
                <option value="public">Public</option>
                <option value="private">Private</option>
                <option value="shared">Shared</option>
              </select>

              <input
                type="text"
                placeholder="Người upload"
                value={state.filters.uploaderEmail}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, uploaderEmail: e.target.value }
                }))}
                className="input min-w-[200px]"
              />

              <button 
                onClick={handleSearch}
                className="btn-primary"
              >
                Tìm kiếm
              </button>
            </div>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex justify-between items-center mb-6 animate-fade-in">
          <div className="flex items-center gap-3">
            {state.showBulkActions && (
              <div className="flex items-center gap-2 animate-slide-in">
                <span className="text-sm text-gray-600">
                  {state.selectedItems.size} đã chọn
                </span>
                <button 
                  onClick={handleBulkDelete}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa các mục đã chọn"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
                <button className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors">
                  <ArchiveBoxIcon className="w-4 h-4" />
                </button>
                <button className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors">
                  <ShareIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setState(prev => ({
                ...prev,
                viewMode: prev.viewMode === 'list' ? 'grid' : 'list'
              }))}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={state.viewMode === 'list' ? 'Chế độ lưới' : 'Chế độ danh sách'}
            >
              {state.viewMode === 'list' ? 
                <ViewColumnsIcon className="w-4 h-4" /> : 
                <Bars3Icon className="w-4 h-4" />
              }
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="card animate-fade-in">
          {state.viewMode === 'list' ? (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      checked={state.selectedItems.size > 0 && state.selectedItems.size === (state.folders.length + state.files.length)}
                    />
                  </div>
                  <div className="col-span-1">Loại</div>
                  <div className="col-span-3">
                    <button
                      onClick={() => handleSort('name')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Tên
                      {state.sorting.field === 'name' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-2">Người sở hữu</div>
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort('createdAt')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Sửa đổi lần cuối
                      {state.sorting.field === 'createdAt' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-1">Kích thước</div>
                  <div className="col-span-2">Thao tác</div>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {state.loading ? (
                  <div className="px-6 py-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                  </div>
                ) : (
                  <>
                    {/* Folders */}
                    {state.folders.map((folder, index) => (
                      <div 
                        key={folder.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${index * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(folder.id)}
                              onChange={() => toggleItemSelection(folder.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            <FolderIcon className="w-6 h-6 text-blue-500" />
                          </div>
                          <div className="col-span-3">
                            <button
                              onClick={() => navigateToFolder(folder.id)}
                              className="font-medium text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              {folder.name}
                            </button>
                            {folder.description && (
                              <p className="text-sm text-gray-500 mt-1">{folder.description}</p>
                            )}
                            <div className="text-xs text-gray-400 mt-1">
                              {folder.fileCount} files, {folder.subfolderCount} folders
                            </div>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            <div className="flex flex-col">
                              <span className="font-medium">{folder.ownerName || 'Unknown'}</span>
                              <span className="text-xs text-gray-400">{folder.ownerId}</span>
                            </div>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(folder.updatedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            --
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                                title="Edit"
                              >
                                <PencilIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Share"
                              >
                                <ShareIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Files */}
                    {state.files.map((file, index) => (
                      <div 
                        key={file.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${(state.folders.length + index) * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(file.id)}
                              onChange={() => toggleItemSelection(file.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            <DocumentIcon className="w-6 h-6 text-gray-500" />
                          </div>
                          <div className="col-span-3">
                            <p className="font-medium text-gray-900">{file.name}</p>
                            {file.description && (
                              <p className="text-sm text-gray-500 mt-1">{file.description}</p>
                            )}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {file.ownerId}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(file.updatedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            {formatFileSize(file.fileSize)}
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                                title="Edit"
                              >
                                <PencilIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Share"
                              >
                                <ShareIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Empty State */}
                    {!state.loading && state.folders.length === 0 && state.files.length === 0 && (
                      <div className="px-6 py-12 text-center animate-fade-in">
                        <FolderIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {!state.currentFolder ? 'Chưa có folders nào' : 'Không có folders hoặc files'}
                        </h3>
                        <p className="text-gray-500 mb-4">
                          {!state.currentFolder 
                            ? 'Tạo folder đầu tiên để bắt đầu tổ chức files của bạn. Files được lưu trữ bên trong các folders.'
                            : 'Tạo folder mới hoặc upload files để bắt đầu tổ chức nội dung.'
                          }
                        </p>
                        <button
                          onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                          className="btn-primary"
                        >
                          Tạo Folder Mới
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </>
          ) : (
            /* Grid View */
            <div className="p-6">
              {state.loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* Folders in Grid */}
                  {state.folders.map((folder, index) => (
                    <div 
                      key={folder.id} 
                      className="card p-4 hover:shadow-md transition-all cursor-pointer animate-fade-in"
                      style={{ animationDelay: `${index * 0.05}s` }}
                      onClick={() => navigateToFolder(folder.id)}
                    >
                      <div className="text-center">
                        <FolderIcon className="w-12 h-12 text-blue-500 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-900 truncate" title={folder.name}>
                          {folder.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {folder.fileCount} files, {folder.subfolderCount} folders
                        </p>
                        <p className="text-xs text-gray-400 mt-1" title={folder.ownerName || 'Unknown'}>
                          {folder.ownerName || 'Unknown'}
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(folder.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleItemSelection(folder.id);
                          }}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Files in Grid */}
                  {state.files.map((file, index) => (
                    <div 
                      key={file.id} 
                      className="card p-4 hover:shadow-md transition-all animate-fade-in"
                      style={{ animationDelay: `${(state.folders.length + index) * 0.05}s` }}
                    >
                      <div className="text-center">
                        <DocumentIcon className="w-12 h-12 text-gray-500 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-900 truncate" title={file.name}>
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatFileSize(file.fileSize)}
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(file.id)}
                          onChange={() => toggleItemSelection(file.id)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Empty State for Grid */}
                  {!state.loading && state.folders.length === 0 && state.files.length === 0 && (
                    <div className="col-span-full text-center py-12 animate-fade-in">
                      <FolderIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {!state.currentFolder ? 'Chưa có folders nào' : 'Không có folders hoặc files'}
                      </h3>
                      <p className="text-gray-500 mb-4">
                        {!state.currentFolder 
                          ? 'Tạo folder đầu tiên để bắt đầu tổ chức files của bạn. Files được lưu trữ bên trong các folders.'
                          : 'Tạo folder mới hoặc upload files để bắt đầu tổ chức nội dung.'
                        }
                      </p>
                      <button
                        onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                        className="btn-primary"
                      >
                        Tạo Folder Mới
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Pagination */}
        {state.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 animate-fade-in">
            <div className="text-sm text-gray-700">
              Hiển thị {((state.pagination.page - 1) * state.pagination.pageSize) + 1} đến{' '}
              {Math.min(state.pagination.page * state.pagination.pageSize, state.pagination.totalItems)} của{' '}
              {state.pagination.totalItems} kết quả
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page === 1}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNumber = Math.max(1, state.pagination.page - 2) + i;
                if (pageNumber <= state.pagination.totalPages) {
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        pageNumber === state.pagination.page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                }
                return null;
              })}

              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page === state.pagination.totalPages}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Folder Modal */}
      <CreateFolderModal
        isOpen={state.showNewFolderModal}
        onClose={() => setState(prev => ({ ...prev, showNewFolderModal: false }))}
        onSubmit={handleCreateFolder}
        currentFolder={state.currentFolder}
        loading={state.loading}
      />
    </div>
  );
} 