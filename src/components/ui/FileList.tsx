"use client";

import React, { useState } from "react";
import { FileListProps, FileSystemItem } from "@/types";
import {
  DocumentIcon,
  FolderIcon,
  EyeIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  PencilIcon,
} from "@heroicons/react/24/outline";

export function FileList({
  files,
  onFileSelect,
  onFileDelete,
  onFolderCreate,
}: FileListProps) {
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  const handleFileClick = (file: FileSystemItem) => {
    setSelectedFile(file.id);
    onFileSelect?.(file);
  };

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      onFolderCreate?.(newFolderName.trim());
      setNewFolderName("");
      setShowCreateFolder(false);
    }
  };

  const handleDeleteFile = (fileId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm("Are you sure you want to delete this file?")) {
      onFileDelete?.(fileId);
    }
  };

  // Separate files and folders with null safety
  const folders = (files || []).filter((item) => item.type === "folder");
  const documents = (files || []).filter((item) => item.type === "file");

  return (
    <div className="card animate-fade-in">
      {/* Header */}
      <div
        className="px-6 py-4"
        style={{ borderBottom: "1px solid var(--border-light)" }}
      >
        <div className="flex items-center justify-between">
          <h2 className="text-heading-3">Files</h2>
          <button
            onClick={() => setShowCreateFolder(true)}
            className="btn-primary"
          >
            New Folder
          </button>
        </div>

        {/* Create folder form */}
        {showCreateFolder && (
          <div className="mt-4 flex gap-2">
            <input
              type="text"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="input flex-1"
              onKeyPress={(e) => e.key === "Enter" && handleCreateFolder()}
            />
            <button
              onClick={handleCreateFolder}
              className="btn-primary"
              style={{ background: "var(--accent-green)" }}
            >
              Create
            </button>
            <button
              onClick={() => {
                setShowCreateFolder(false);
                setNewFolderName("");
              }}
              className="btn-secondary"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* File list */}
      <div className="divide-y divide-gray-200">
        {(!files || files.length === 0) ? (
          <div className="px-6 py-12 text-center">
            <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No files</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by uploading a PDF file.
            </p>
          </div>
        ) : (
          <>
            {/* Folders */}
            {folders.map((folder) => (
              <div
                key={folder.id}
                className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${
                  selectedFile === folder.id ? "bg-blue-50" : ""
                }`}
                onClick={() => handleFileClick(folder)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FolderIcon className="h-8 w-8 text-blue-500 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {folder.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        Folder • {formatDate(folder.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => handleDeleteFile(folder.id, e)}
                      className="p-1 text-gray-400 hover:text-red-600"
                      title="Delete folder"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {/* Files */}
            {documents.map((file) => (
              <div
                key={file.id}
                className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${
                  selectedFile === file.id ? "bg-blue-50" : ""
                }`}
                onClick={() => handleFileClick(file)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <DocumentIcon className="h-8 w-8 text-red-500 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {file.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {file.size && formatFileSize(file.size)} •{" "}
                        {formatDate(file.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle view action
                      }}
                      className="p-1 text-gray-400 hover:text-blue-600"
                      title="View file"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle download action
                      }}
                      className="p-1 text-gray-400 hover:text-green-600"
                      title="Download file"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle rename action
                      }}
                      className="p-1 text-gray-400 hover:text-yellow-600"
                      title="Rename file"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => handleDeleteFile(file.id, e)}
                      className="p-1 text-gray-400 hover:text-red-600"
                      title="Delete file"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
}
