import { useState, useCallback, useMemo } from "react";
import { FileService } from "@/api/services/fileService";
import { FileDto } from "@/api/types/interfaces";
import { FileItem, UploadProgress } from "@/types";
import { validateFile } from "@/lib/utils";
import { FileUploadError, logError } from "@/lib/error-handler";
import {
  MAX_FILE_SIZE,
  ACCEPTED_FILE_TYPES,
  ERROR_MESSAGES,
} from "@/lib/constants";

export interface UseUploadOptions {
  maxSize?: number;
  acceptedTypes?: Record<string, string[]>;
  multiple?: boolean;
  autoUpload?: boolean;
  folderId?: string;
  fileService?: FileService; // Add FileService instance
  onFileUploaded?: (file: FileItem) => void;
  onProgressUpdate?: (fileId: string, progress: UploadProgress) => void;
}

export function useUpload(options: UseUploadOptions = {}) {
  const {
    maxSize = MAX_FILE_SIZE,
    acceptedTypes = ACCEPTED_FILE_TYPES,
    multiple = true,
    autoUpload = false,
    folderId,
    fileService,
    onFileUploaded,
    onProgressUpdate,
  } = options;

  const [isUploading, setIsUploading] = useState(false);
  const [uploadQueue, setUploadQueue] = useState<File[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<
    Record<string, UploadProgress>
  >({});

  // Memoize validation configuration
  const validationConfig = useMemo(
    () => ({
      maxSize,
      acceptedTypes,
    }),
    [maxSize, acceptedTypes]
  );

  // Validate files using improved utility function
  const validateFiles = useCallback(
    (files: File[]): { valid: File[]; errors: string[] } => {
      const validFiles: File[] = [];
      const validationErrors: string[] = [];

      files.forEach((file) => {
        const validation = validateFile(
          file,
          validationConfig.maxSize,
          validationConfig.acceptedTypes
        );

        if (validation.isValid) {
          validFiles.push(file);
        } else {
          validationErrors.push(`${file.name}: ${validation.error}`);
        }
      });

      return { valid: validFiles, errors: validationErrors };
    },
    [validationConfig]
  );

  // Helper functions for progress management
  const updateProgress = useCallback(
    (fileId: string, progress: UploadProgress) => {
      setUploadProgress((prev) => ({ ...prev, [fileId]: progress }));
      onProgressUpdate?.(fileId, progress);
    },
    [onProgressUpdate]
  );

  const removeProgress = useCallback((fileId: string) => {
    setUploadProgress((prev) => {
      const { [fileId]: removed, ...rest } = prev;
      return rest;
    });
  }, []);

  // Generate unique file ID
  const generateFileId = useCallback(() => {
    return `upload-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }, []);

  // Upload single file
  const uploadFile = useCallback(
    async (file: File, targetFolderId?: string): Promise<FileItem> => {
      if (!fileService) {
        throw new FileUploadError(
          "File service not available. Please ensure you are authenticated.",
          file.name
        );
      }

      const fileId = generateFileId();
      const targetFolder = targetFolderId ?? folderId;

      // Set initial progress
      const initialProgress: UploadProgress = {
        fileId,
        fileName: file.name,
        progress: 0,
        status: "uploading",
      };
      updateProgress(fileId, initialProgress);

      try {
        // Upload file using the new API service
        const uploadedFileDto = await fileService.upload(file, {
          parentFolderId: targetFolder || undefined,
          displayName: file.name,
          description: `Uploaded via hook: ${file.name}`,
        });

        // Update progress to completed
        updateProgress(fileId, {
          ...initialProgress,
          progress: 100,
          status: "completed",
        });

        // Convert FileDto to FileItem for backward compatibility
        const uploadedFile: FileItem = {
          id: uploadedFileDto.id,
          name: uploadedFileDto.name,
          size: uploadedFileDto.fileSize,
          type: uploadedFileDto.mimeType,
          uploadedAt: uploadedFileDto.createdAt,
          folderId: uploadedFileDto.parentFolderId || null,
          url: '', // Will need to be set separately if needed
        };

        // Notify parent component
        onFileUploaded?.(uploadedFile);

        // Remove progress after a delay
        setTimeout(() => {
          removeProgress(fileId);
        }, 2000);

        return uploadedFile;
      } catch (error) {
        const uploadError = new FileUploadError(
          error instanceof Error ? error.message : ERROR_MESSAGES.UPLOAD_FAILED,
          file.name,
          { fileSize: file.size, fileType: file.type }
        );

        logError(uploadError, `File upload: ${file.name}`);

        updateProgress(fileId, {
          ...initialProgress,
          status: "error",
          error: uploadError.message,
        });

        // Remove progress after a delay
        setTimeout(() => {
          removeProgress(fileId);
        }, 5000);

        throw uploadError;
      }
    },
    [folderId, fileService, updateProgress, removeProgress, onFileUploaded, generateFileId]
  );

  // Upload multiple files
  const uploadFiles = useCallback(
    async (files: File[], targetFolderId?: string): Promise<FileItem[]> => {
      const { valid: validFiles, errors: validationErrors } =
        validateFiles(files);

      setErrors(validationErrors);

      if (validFiles.length === 0) {
        return [];
      }

      setIsUploading(true);
      const uploadedFiles: FileItem[] = [];
      const uploadErrors: string[] = [];

      try {
        // Upload files sequentially to avoid overwhelming the server
        for (const file of validFiles) {
          try {
            const uploadedFile = await uploadFile(file, targetFolderId);
            uploadedFiles.push(uploadedFile);
          } catch (error) {
            const errorMessage =
              error instanceof Error
                ? error.message
                : ERROR_MESSAGES.UPLOAD_FAILED;
            uploadErrors.push(`${file.name}: ${errorMessage}`);
          }
        }

        setErrors((prev) => [...prev, ...uploadErrors]);
        return uploadedFiles;
      } finally {
        setIsUploading(false);
      }
    },
    [validateFiles, uploadFile]
  );

  // Add files to queue
  const addToQueue = useCallback(
    (files: File[]) => {
      const { valid: validFiles, errors: validationErrors } =
        validateFiles(files);

      setErrors(validationErrors);
      setUploadQueue((prev) => [...prev, ...validFiles]);

      if (autoUpload && validFiles.length > 0) {
        uploadFiles(validFiles);
      }
    },
    [validateFiles, autoUpload, uploadFiles]
  );

  // Process queue
  const processQueue = useCallback(
    async (targetFolderId?: string): Promise<FileItem[]> => {
      if (uploadQueue.length === 0) return [];

      const files = [...uploadQueue];
      setUploadQueue([]);

      return await uploadFiles(files, targetFolderId);
    },
    [uploadQueue, uploadFiles]
  );

  // Clear queue
  const clearQueue = useCallback(() => {
    setUploadQueue([]);
  }, []);

  // Clear errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  return {
    // State
    isUploading,
    uploadQueue,
    errors,
    uploadProgress,
    hasErrors: errors.length > 0,
    hasQueue: uploadQueue.length > 0,

    // Actions
    uploadFile,
    uploadFiles,
    addToQueue,
    processQueue,
    clearQueue,
    clearErrors,
    validateFiles,

    // Configuration
    maxSize,
    acceptedTypes,
    multiple,
  };
}
